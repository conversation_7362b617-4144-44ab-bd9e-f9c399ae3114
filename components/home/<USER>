import { DiscoBallsSvg } from '@/components/svgs/DiscoBallsSvg';
import Image from 'next/image';
import { TitleSvg } from '../svgs';

type MainTitleProps = {
  className?: string;
};

export function MainTitle({
  className = '',
}: MainTitleProps) {
  return (
    <div className={className}>

      {/* Main Title Section with layered images */}
      <div className="relative my-8 flex items-center justify-center">
        {/* Couple Photo - Base layer */}
        <div className="relative w-64 h-80 md:w-80 md:h-96 lg:w-96 lg:h-[480px]">
          <Image
            src="/sean_and_eva_banner_photo.png"
            alt="Sean and Eva"
            fill
            className="object-cover rounded-lg"
            priority
          />

          {/* Title SVG - Positioned above the photo */}
          <div className="absolute left-1/2 transform -translate-x-1/2 w-full">
            <div className="relative w-full h-48 md:h-64 lg:h-80">
              <TitleSvg className="w-full h-full object-contain" />
            </div>
          </div>

          {/* Disco Balls SVG - Overlaid to surround the photo with larger size */}
          <div className="absolute inset-0 -m-16 md:-m-24 lg:-m-32">
            <div className="relative w-full h-full scale-125 md:scale-150 lg:scale-175">
              <DiscoBallsSvg className="w-full h-full object-contain" />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
